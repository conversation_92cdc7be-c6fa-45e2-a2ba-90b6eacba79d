==========
Intro
==========

This is a short introduction to zvt, you would learn the basic usage and
glance a global view here.

Rethink market and programming
------------------------------
For practical trading, complex algorithm is fragile, complex algorithm building
on complex facility is more fragile, complex algorithm building on complex
facility by complex team is more and more fragile.

zvt want to provide a simple facility for building straightforward algorithm, it
should be:

* **use the most basic programming concepts**

* **concise abstraction of the market**

* **correctness is obvious**

Core concepts building zvt
------------------------------
| Technologies come and technologies go, but market insight is forever.
| Your world is built by core concepts inside you, so it's you.
| zvt world is built by core concepts inside market, so it's zvt.
| We would show how core concepts building zvt in four aspects：

.. toctree::
    :maxdepth: 2

    data/index
    factor/index
    trader/index
    ml/index
