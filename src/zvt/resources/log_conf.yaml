version: 1
disable_existing_loggers: False
formatters:
  default:
    # "()": uvicorn.logging.DefaultFormatter
    format: '%(asctime)s  %(levelname)s  %(threadName)s  %(message)s'
  access:
    # "()": uvicorn.logging.AccessFormatter
    format: '%(asctime)s  %(levelname)s  %(threadName)s  %(message)s'
handlers:
  default:
    formatter: default
    class: logging.StreamHandler
    stream: ext://sys.stderr
  file:
    class: logging.handlers.RotatingFileHandler
    formatter: default
    filename: server.log
    maxBytes: 524288000
    level: INFO
    backupCount: 10
  access:
    formatter: access
    class: logging.StreamHandler
    stream: ext://sys.stdout
loggers:
  uvicorn.error:
    level: INFO
    handlers:
      - default
    propagate: no
  uvicorn.access:
    level: INFO
    handlers:
      - access
    propagate: no
root:
  level: INFO
  handlers:
    - default
    - file
  propagate: no