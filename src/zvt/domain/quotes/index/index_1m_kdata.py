# -*- coding: utf-8 -*-
# this file is generated by gen_kdata_schema function, dont't change it
from sqlalchemy.orm import declarative_base

from zvt.contract import TradableEntity
from zvt.contract.register import register_schema
from zvt.domain.quotes import IndexKdataCommon

KdataBase = declarative_base()


class Index1mKdata(KdataBase, IndexKdataCommon, TradableEntity):
    __tablename__ = "index_1m_kdata"


register_schema(providers=["em", "sina", "qmt"], db_name="index_1m_kdata", schema_base=KdataBase, entity_type="index")


# the __all__ is generated
__all__ = ["Index1mKdata"]
